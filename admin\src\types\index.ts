// Export all types from a single entry point
export type {
  Customer,
  CustomerProfile,
  CustomerListItem,
  CustomerStats,
  CustomerOrderSummary,
  CustomerNote,
  CreateNoteData,
  CreateCustomerData,
  CreatedCustomerResponse
} from './customer';

export type {
  Order,
  OrderItem,
  ShowroomStatus,
  ChainOrderForm
} from './order';

export {
  SHIPPING_UNIT_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
  SHOWROOM_STATUS_OPTIONS,
  STATUS_OPTIONS
} from './order';

export type {
  Product,
  ProductVariant,
  ProductImage,
  VariantImage,
  Category,
  ProductListItem
} from './product';

export type {
  Staff,
  StaffRole,
  StaffRoleOption
} from './staff';

export {
  STAFF_ROLE_OPTIONS,
  STAFF_ROLE_DISPLAY_MAP
} from './staff';

export type {
  ApiErrorResponse,
  ApiError
} from './api';

export { isApiError } from './api';

export type {
  DashboardStats,
  ProductRevenueItem,
  ProductRevenueResponse,
  RevenueComparison,
  DeliveryRevenueItem,
  DeliveryRevenueResponse,
  TopCustomerItem,
  TopCustomerResponse,
  OrderByOption,
  LimitOption
} from './report';
