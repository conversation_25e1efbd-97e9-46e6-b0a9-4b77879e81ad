import { Staff } from "./staff";

export type ShowroomStatus = "transfer_to_warehouse" | "transfer_to_showroom" | "cancel_damaged" | "showroom_order" | null;

export interface OrderItem {
  id: number;
  product: number;
  product_name: string;
  product_chain_price?: number;
  product_code?: string;
  variant?: number;
  variant_name?: string;
  quantity: number;
  price: number;
  total_price: number;
  product_weight?: number;
  unit?: string;
  chiet_khau_amount?: number;
}

export interface Promotion {
  id: number;
  code: string;
  description: string;
  image?: string;
  image_url?: string;
  value: string;
  value_display: string;
  is_percentage: boolean;
  min_purchase_amount: string;
  usage_limit: number;
  usage_count: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
}

export interface OrderPromotion {
  id: number;
  promotion: Promotion;
  discount_amount: string;
  applied_at: string;
}

export interface Order {
  id: number;
  user: {
    id: number;
    email: string;
    username: string;
    full_name: string;
    rank: "gold" | "silver" | "normal";
  };
  // status: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "returned" | "refunded";
  status: "pending" | "accounting_processing" | "processing" | "shipped" | "delivered" | "cancelled" | "returned";
  status_display: string;
  total_price: number;
  final_total: number;
  shipping_address: string;
  ward?: string;
  district?: string;
  city?: string;
  phone_number: string;
  email: string;
  created_at: string;
  updated_at: string;
  confirmation_time?: string; // Thời gian xác nhận đơn hàng
  completion_time?: string; // Thời gian hoàn thành đơn hàng
  sales_admin?: Staff;
  delivery_staff?: Staff;
  delivery_date?: string;
  delivery_time?: string;
  shipping_unit?: "company_vehicle" | "motorbike" | "grab" | "transport_partner" | "shipping_partner";
  shipping_fee: number;
  payment_method?: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  discount: number;
  tax: number;
  have_tax: boolean;
  subtotal_with_product_discounts?: number;
  total_product_discount?: number;
  notes: string;
  items: OrderItem[];
  promotions?: OrderPromotion[];
  is_showroom: boolean;
  showroom_status: ShowroomStatus;
  showroom_status_display?: string;
  is_printed: boolean;
  is_e_comm: boolean;
  is_chain: boolean;
}

export const SHIPPING_UNIT_OPTIONS = [
  { value: "company_vehicle", label: "Xe Cty" },
  { value: "motorbike", label: "Xe máy" },
  { value: "grab", label: "Grab" },
  { value: "transport_partner", label: "Chành xe" },
  { value: "shipping_partner", label: "ĐVVC" },
] as const;

export const PAYMENT_METHOD_OPTIONS = [
  { value: "cod", label: "COD" },
  { value: "cash", label: "Tiền mặt" },
  { value: "bank_transfer", label: "Chuyển Khoản" },
] as const;

export const SHOWROOM_STATUS_OPTIONS = [
  { value: "transfer_to_warehouse", label: "Điều chuyển hàng từ SR về Kho" },
  { value: "transfer_to_showroom", label: "Điều chuyển hàng từ Kho về SR" },
  { value: "cancel_damaged", label: "Huỷ hàng hư hỏng của SR" },
  { value: "showroom_order", label: "Đặt hàng của SR" },
] as const;

export const PAYMENT_STATUS_OPTIONS = [
  { value: "paid", label: "Đã thanh toán" },
  { value: "unpaid", label: "Chưa thanh toán" },
] as const;

export const STATUS_OPTIONS = [
  { value: "pending", label: "Chờ xác nhận" },
  { value: "accounting_processing", label: "Đang xử lý (kế toán)" },
  { value: "processing", label: "Đang xử lý" },
  { value: "shipped", label: "Đang giao hàng" },
  { value: "delivered", label: "Hoàn thành" },
  { value: "cancelled", label: "Hủy bỏ" },
  { value: "returned", label: "Đã trả hàng" },
  // { value: "refunded", label: "Đã hoàn tiền" },
] as const;

export const CHAIN_STATUS_OPTIONS = [
  { value: "accounting_processing", label: "Đang xử lý (kế toán)" },
  { value: "processing", label: "Đang xử lý" },
  { value: "shipped", label: "Đang giao hàng" },
  { value: "delivered", label: "Hoàn thành" },
  { value: "cancelled", label: "Hủy bỏ" },
  { value: "returned", label: "Đã trả hàng" },
] as const;

export interface ChainOrderForm {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_fee?: number;
  discount?: number;
  is_chain: boolean;
  tax?: number;
  have_tax: boolean;
  sales_admin?: number;
}
